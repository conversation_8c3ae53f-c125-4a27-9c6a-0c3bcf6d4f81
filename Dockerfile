# Use Node.js 18 LTS
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Build frontend
COPY . .
RUN npm run build

# Copy application files
COPY server.cjs ./
COPY public/ ./public/

# Create dist directory if it doesn't exist
RUN mkdir -p dist

# Expose port (Cloud Run uses PORT env var)
EXPOSE $PORT

# Set environment variables
ENV NODE_ENV=production

# Start the server
CMD ["node", "server.cjs"]
