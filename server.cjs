const express = require('express');
const cors = require('cors');
const fs = require('fs').promises;
const path = require('path');
const { QRCodeStyling } = require('qr-code-styling/lib/qr-code-styling.common.js');
const nodeCanvas = require('canvas');
const { JSDOM } = require('jsdom');
const crypto = require('crypto');
const os = require('os');
const PDFDocument = require('pdfkit');
const bidi = require('bidi-js');
const admin = require('firebase-admin');

// __dirname is available in CommonJS

// Initialize Firebase Admin
const firebaseConfig = {
  apiKey: "AIzaSyDzhwOOnCbpkuADs_T70SuocZ5ZmtNe4cY",
  authDomain: "textbook-platform.firebaseapp.com",
  projectId: "textbook-platform",
  storageBucket: "textbook-platform.firebasestorage.app",
  messagingSenderId: "376480732351",
  appId: "1:376480732351:web:95a0cd1e6837c0c3d2220a"
};

admin.initializeApp({
  projectId: firebaseConfig.projectId,
});

const app = express();
const PORT = 3001;
const BOOKS_FILE = path.join(__dirname, 'public', 'books.json');

app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static('public'));

// Helper function to read books
async function readBooks() {
  try {
    const data = await fs.readFile(BOOKS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading books:', error);
    return [];
  }
}

// Helper function to write books
async function writeBooks(books) {
  try {
    await fs.writeFile(BOOKS_FILE, JSON.stringify(books, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing books:', error);
    return false;
  }
}

// Helper function to generate meaningful IDs
function generateMeaningfulId(type, books, bookId = null, chapterId = null) {
  switch (type) {
    case 'book':
      const bookCount = books.length + 1;
      return `book${bookCount}`;

    case 'chapter':
      const book = books.find(b => b.id === bookId);
      if (!book) return `ch${Date.now()}`;
      const chapterCount = book.chapters.length + 1;
      return `${bookId}-ch${chapterCount}`;

    case 'item':
      const targetBook = books.find(b => b.id === bookId);
      if (!targetBook) return `item${Date.now()}`;
      const targetChapter = targetBook.chapters.find(c => c.id === chapterId);
      if (!targetChapter) return `item${Date.now()}`;
      const itemCount = targetChapter.items.length + 1;
      return `${chapterId}-item${itemCount}`;

    default:
      return Date.now().toString();
  }
}

// Generate secure token for QR access
function generateSecureToken() {
  return crypto.randomBytes(32).toString('hex');
}

// Get the network IP address
function getNetworkIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  return 'localhost'; // fallback
}

// Store for QR tokens (in production, use Redis or database)
const qrTokens = new Map();

// Store for user sessions (in production, use Redis or database)
const userSessions = new Map();

// Default users (in production, use proper database with hashed passwords)
const defaultUsers = [
  {
    id: 'admin-1',
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123', // In production, this should be hashed
    role: 'admin',
    createdAt: Date.now()
  },
  {
    id: 'editor-1',
    username: 'editor',
    email: '<EMAIL>',
    password: 'editor123', // In production, this should be hashed
    role: 'editor',
    createdAt: Date.now()
  },
  {
    id: 'viewer-1',
    username: 'viewer',
    email: '<EMAIL>',
    password: 'viewer123', // In production, this should be hashed
    role: 'viewer',
    createdAt: Date.now()
  }
];

// Firebase authentication middleware with fallback
async function authenticateToken(req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Access token required' });
    }

    try {
      // Try Firebase ID token verification first
      const decodedToken = await admin.auth().verifyIdToken(token);

      // Get user profile from Firestore
      const userDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();

      if (!userDoc.exists) {
        return res.status(403).json({ error: 'User profile not found' });
      }

      const userData = userDoc.data();
      req.user = {
        id: decodedToken.uid,
        uid: decodedToken.uid,
        email: decodedToken.email,
        username: userData.username,
        role: userData.role
      };

      next();
    } catch (firebaseError) {
      console.log('Firebase auth failed, trying fallback:', firebaseError.message);

      // Fallback to old session-based authentication for development
      const session = userSessions.get(token);
      if (!session) {
        return res.status(403).json({ error: 'Invalid or expired token' });
      }

      req.user = session.user;
      next();
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
}

// Permission check middleware
function requirePermission(permission) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const userPermissions = getRolePermissions(req.user.role);
    if (!userPermissions[permission]) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

// Get role permissions
function getRolePermissions(role) {
  switch (role) {
    case 'viewer':
      return {
        canView: true,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'editor':
      return {
        canView: true,
        canEdit: true,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
    case 'admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: false, // Content admin - no user management
        canCreateBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    case 'super-admin':
      return {
        canView: true,
        canEdit: true,
        canDelete: true,
        canManageUsers: true, // Only super-admin can manage users
        canCreateBooks: true,
        canDeleteBooks: true,
        canCreateChapters: true,
        canDeleteChapters: true,
      };
    default:
      return {
        canView: false,
        canEdit: false,
        canDelete: false,
        canManageUsers: false,
        canCreateBooks: false,
        canDeleteBooks: false,
        canCreateChapters: false,
        canDeleteChapters: false,
      };
  }
}

// Helper function to create smooth QR code with rounded modules
async function createSmoothQRCode(data, itemType, color = '#1f2937', backgroundColor = '#ffffff') {
  try {
    console.log(`Generating QR code for ${itemType} with color ${color} and background ${backgroundColor}`);
    const colors = getColorsFromHex(color, backgroundColor);

    const qrCodeOptions = {
      width: 800,  // Increased from 280 to 800 for better print quality
      height: 800, // Increased from 280 to 800 for better print quality
      margin: 40,  // Add padding around the QR code for better scanning
      data: data,
      dotsOptions: {
        color: colors.dark,
        type: "rounded"
      },
      backgroundOptions: {
        color: colors.light,
      },
      cornersSquareOptions: {
        color: colors.dark,
        type: "extra-rounded"
      },
      cornersDotOptions: {
        color: colors.dark,
        type: "dot"
      },
      imageOptions: {
        margin: 20  // Add padding/margin around the QR code for better scanning
      }
    };

    // Try to add icon if available
    const iconPath = path.join(__dirname, 'public', 'icons', `${itemType}.svg`);
    try {
      await fs.access(iconPath);
      console.log(`Using icon: ${iconPath}`);
      qrCodeOptions.image = iconPath;
      qrCodeOptions.imageOptions = {
        crossOrigin: "anonymous",
        margin: 20,
        imageSize: 0.3,
        hideBackgroundDots: true
      };
    } catch (error) {
      console.log(`Icon not found for type: ${itemType}, using default`);
    }

    console.log('Creating QRCodeStyling instance...');
    // Use the correct Node.js syntax from the documentation
    const qrCode = new QRCodeStyling({
      jsdom: JSDOM, // this is required
      nodeCanvas, // this is required
      ...qrCodeOptions,
      imageOptions: {
        saveAsBlob: true,
        crossOrigin: "anonymous",
        margin: 15,
        imageSize: 0.25,
        hideBackgroundDots: true,
        ...qrCodeOptions.imageOptions
      }
    });

    console.log('Getting raw data as buffer...');
    // Use getRawData with proper type specification
    const buffer = await qrCode.getRawData("png");

    if (!buffer) {
      throw new Error('Failed to generate QR code buffer');
    }

    console.log('QR code generated successfully, buffer size:', buffer.length);
    return buffer;

  } catch (error) {
    console.error('Error in createSmoothQRCode:', error);
    console.error('Error details:', error.message);
    throw new Error(`QR code generation failed: ${error.message}`);
  }
}

// Get colors from hex color
function getColorsFromHex(hexColor, backgroundColor) {
  // Validate hex color format
  if (!hexColor || !hexColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    hexColor = '#1f2937'; // Default gray
  }

  // Validate background color format
  if (!backgroundColor || !backgroundColor.match(/^#[0-9A-Fa-f]{6}$/)) {
    backgroundColor = '#ffffff'; // Default white
  }

  return {
    dark: hexColor,
    light: backgroundColor
  };
}

// Authentication endpoints
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: 'Username and password required' });
    }

    // Find user (in production, use proper database lookup with hashed passwords)
    const user = defaultUsers.find(u => u.username === username && u.password === password);

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate session token
    const token = crypto.randomBytes(32).toString('hex');
    const session = {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt,
        lastLogin: Date.now()
      },
      createdAt: Date.now()
    };

    // Store session (expires in 24 hours)
    userSessions.set(token, session);
    setTimeout(() => userSessions.delete(token), 24 * 60 * 60 * 1000);

    res.json({
      user: session.user,
      token: token,
      permissions: getRolePermissions(user.role)
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/auth/logout', authenticateToken, async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      userSessions.delete(token);
    }

    res.json({ message: 'Logged out successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/auth/me', authenticateToken, async (req, res) => {
  try {
    res.json({
      user: req.user,
      permissions: getRolePermissions(req.user.role)
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all books (requires authentication)
app.get('/api/books', authenticateToken, requirePermission('canView'), async (req, res) => {
  const books = await readBooks();
  res.json(books);
});

// Add a new book (admin only)
app.post('/api/books', authenticateToken, requirePermission('canCreateBooks'), async (req, res) => {
  try {
    const books = await readBooks();
    const newBook = {
      id: generateMeaningfulId('book', books),
      title: req.body.title,
      description: req.body.description || '',
      chapters: []
    };

    books.push(newBook);
    const success = await writeBooks(books);

    if (success) {
      res.json(newBook);
    } else {
      res.status(500).json({ error: 'Failed to save book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add a chapter to a book (admin only)
app.post('/api/books/:bookId/chapters', authenticateToken, requirePermission('canCreateChapters'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const newChapter = {
      id: generateMeaningfulId('chapter', books, req.params.bookId),
      title: req.body.title,
      items: []
    };

    book.chapters.push(newChapter);
    const success = await writeBooks(books);

    if (success) {
      res.json(newChapter);
    } else {
      res.status(500).json({ error: 'Failed to save chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add an item to a chapter (editor or admin)
app.post('/api/books/:bookId/chapters/:chapterId/items', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const newItem = {
      id: generateMeaningfulId('item', books, req.params.bookId, req.params.chapterId),
      ...req.body
    };

    chapter.items.push(newItem);
    const success = await writeBooks(books);

    if (success) {
      res.json(newItem);
    } else {
      res.status(500).json({ error: 'Failed to save item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a book
app.delete('/api/books/:bookId', async (req, res) => {
  try {
    const books = await readBooks();
    const bookIndex = books.findIndex(b => b.id === req.params.bookId);

    if (bookIndex === -1) {
      return res.status(404).json({ error: 'Book not found' });
    }

    books.splice(bookIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Book deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete book' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete a chapter
app.delete('/api/books/:bookId/chapters/:chapterId', async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapterIndex = book.chapters.findIndex(c => c.id === req.params.chapterId);
    if (chapterIndex === -1) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    book.chapters.splice(chapterIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Chapter deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete chapter' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update an item in a chapter (editor or admin)
app.put('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canEdit'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Update the item while preserving the ID
    const updatedItem = {
      ...chapter.items[itemIndex],
      ...req.body,
      id: req.params.itemId // Ensure ID never changes
    };

    chapter.items[itemIndex] = updatedItem;
    const success = await writeBooks(books);

    if (success) {
      res.json(updatedItem);
    } else {
      res.status(500).json({ error: 'Failed to update item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete an item (admin only)
app.delete('/api/books/:bookId/chapters/:chapterId/items/:itemId', authenticateToken, requirePermission('canDelete'), async (req, res) => {
  try {
    const books = await readBooks();
    const book = books.find(b => b.id === req.params.bookId);

    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === req.params.chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const itemIndex = chapter.items.findIndex(i => i.id === req.params.itemId);
    if (itemIndex === -1) {
      return res.status(404).json({ error: 'Item not found' });
    }

    chapter.items.splice(itemIndex, 1);
    const success = await writeBooks(books);

    if (success) {
      res.json({ message: 'Item deleted successfully' });
    } else {
      res.status(500).json({ error: 'Failed to delete item' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate QR code for an item
app.get('/api/qr/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }

    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with default gray color and white background
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, '#1f2937', '#ffffff');
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Generate stylized QR code with custom colors
app.get('/api/qr-styled/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const color = req.query.color || '#1f2937';
    const backgroundColor = req.query.backgroundColor || '#ffffff';
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).json({ error: 'Chapter not found' });
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).json({ error: 'Item not found' });
    }







    // Create permanent URL using item IDs (no expiring tokens)
    const networkIP = getNetworkIP();
    const displayUrl = `${req.protocol}://${networkIP}:${PORT}/item/${bookId}/${chapterId}/${itemId}`;

    const itemData = {
      bookId,
      chapterId,
      itemId,
      bookTitle: book.title,
      chapterTitle: chapter.title,
      itemTitle: item.title,
      itemType: item.type,
      item: item,
      createdAt: Date.now()
    };

    // Generate smooth QR code with selected colors
    const qrBuffer = await createSmoothQRCode(displayUrl, item.type, color, backgroundColor);
    const qrCodeDataURL = `data:image/png;base64,${qrBuffer.toString('base64')}`;

    res.json({
      qrCode: qrCodeDataURL,
      data: itemData,
      item: item,
      styled: true,
      color: color,
      backgroundColor: backgroundColor,
      displayUrl: displayUrl
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Permanent display page for QR code access using item IDs
app.get('/item/:bookId/:chapterId/:itemId', async (req, res) => {
  try {
    const { bookId, chapterId, itemId } = req.params;
    const books = await readBooks();

    const book = books.find(b => b.id === bookId);
    if (!book) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📚 Book Not Found</h1>
            <p>The requested book could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const chapter = book.chapters.find(c => c.id === chapterId);
    if (!chapter) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Chapter Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📖 Chapter Not Found</h1>
            <p>The requested chapter could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    const item = chapter.items.find(i => i.id === itemId);
    if (!item) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Item Not Found</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>📄 Item Not Found</h1>
            <p>The requested item could not be found.</p>
          </div>
        </body>
        </html>
      `);
    }

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, book.title, chapter.title);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate PDF test results
app.post('/api/generate-test-pdf', async (req, res) => {
  try {
    const testData = JSON.parse(req.body.testData);

    // Create PDF document
    const doc = new PDFDocument({
      size: 'A4',
      margin: 50,
      info: {
        Title: `Test Results - ${testData.testTitle}`,
        Author: testData.userName,
        Subject: `${testData.bookTitle} - ${testData.chapterTitle}`,
        Creator: 'Textbook Platform'
      }
    });

    // Set response headers for PDF download
    const safeTestName = testData.testTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const safeUserName = testData.userName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const filename = `${safeUserName}_${safeTestName}_results.pdf`;

    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Pipe PDF to response
    doc.pipe(res);

    // Helper function for Arabic text support
    function processText(text) {
      try {
        return bidi(text, { dir: 'auto' });
      } catch (error) {
        return text; // Fallback to original text if bidi processing fails
      }
    }

    // Calculate score and percentage
    const percentage = Math.round((testData.score / testData.total) * 100);
    const date = new Date(testData.date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Header section with gradient-like effect using rectangles
    doc.rect(0, 0, doc.page.width, 120)
       .fillAndStroke('#4f46e5', '#4f46e5');

    // Title
    doc.fillColor('#ffffff')
       .fontSize(28)
       .font('Helvetica-Bold')
       .text('🏆 Test Results', 50, 30, { align: 'center' });

    // Test details
    doc.fontSize(16)
       .text(processText(`${testData.bookTitle} → ${testData.chapterTitle}`), 50, 65, { align: 'center' });

    doc.fontSize(18)
       .font('Helvetica-Bold')
       .text(processText(testData.testTitle), 50, 85, { align: 'center' });

    // Student info section
    doc.fillColor('#1e293b')
       .fontSize(20)
       .font('Helvetica-Bold')
       .text(`Student: ${processText(testData.userName)}`, 50, 150);

    doc.fontSize(14)
       .fillColor('#64748b')
       .font('Helvetica')
       .text(`Date: ${date}`, 50, 175);

    // Score section with circular progress simulation
    const centerX = doc.page.width / 2;
    const scoreY = 220;

    // Score background circle
    doc.circle(centerX, scoreY, 60)
       .fillAndStroke('#f1f5f9', '#e2e8f0');

    // Score text
    doc.fillColor('#1a202c')
       .fontSize(24)
       .font('Helvetica-Bold')
       .text(`${testData.score}/${testData.total}`, centerX - 30, scoreY - 15, { width: 60, align: 'center' });

    doc.fontSize(16)
       .fillColor('#64748b')
       .text(`${percentage}%`, centerX - 20, scoreY + 5, { width: 40, align: 'center' });

    // Performance badge
    let performanceText = '';
    let badgeColor = '';
    if (percentage >= 90) {
      performanceText = 'Excellent!';
      badgeColor = '#10b981';
    } else if (percentage >= 80) {
      performanceText = 'Great Job!';
      badgeColor = '#10b981';
    } else if (percentage >= 70) {
      performanceText = 'Good Work!';
      badgeColor = '#f59e0b';
    } else if (percentage >= 60) {
      performanceText = 'Keep Trying!';
      badgeColor = '#f59e0b';
    } else {
      performanceText = 'Study More!';
      badgeColor = '#ef4444';
    }

    doc.rect(centerX - 50, scoreY + 40, 100, 25)
       .fillAndStroke(badgeColor, badgeColor);

    doc.fillColor('#ffffff')
       .fontSize(12)
       .font('Helvetica-Bold')
       .text(performanceText, centerX - 45, scoreY + 48, { width: 90, align: 'center' });

    // Questions review section
    let currentY = 350;
    doc.fillColor('#1e293b')
       .fontSize(20)
       .font('Helvetica-Bold')
       .text('Question Review', 50, currentY);

    currentY += 40;

    // Process each question
    testData.questions.forEach((question, index) => {
      if (currentY > doc.page.height - 150) {
        doc.addPage();
        currentY = 50;
      }

      const isCorrect = testData.userAnswers[index] === question.correctAnswer;
      const userAnswer = testData.userAnswers[index] !== undefined ?
        question.options[testData.userAnswers[index]]?.text || 'Not answered' : 'Not answered';
      const correctAnswer = question.options[question.correctAnswer]?.text || 'No answer';

      // Question card background
      const cardColor = isCorrect ? '#ecfdf5' : '#fef2f2';
      const borderColor = isCorrect ? '#10b981' : '#ef4444';

      doc.rect(50, currentY - 5, doc.page.width - 100, 80)
         .fillAndStroke(cardColor, borderColor);

      // Status icon
      doc.fillColor(borderColor)
         .fontSize(16)
         .font('Helvetica-Bold')
         .text(isCorrect ? '✓' : '✗', 65, currentY + 10);

      // Question text
      doc.fillColor('#1e293b')
         .fontSize(14)
         .font('Helvetica-Bold')
         .text(`Q${index + 1}: ${processText(question.question)}`, 90, currentY + 5, {
           width: doc.page.width - 150,
           height: 20,
           ellipsis: true
         });

      // User answer
      doc.fillColor('#64748b')
         .fontSize(12)
         .font('Helvetica')
         .text(`Your answer: ${processText(userAnswer)}`, 90, currentY + 25, {
           width: doc.page.width - 150,
           height: 15,
           ellipsis: true
         });

      // Correct answer (if wrong)
      if (!isCorrect) {
        doc.fillColor('#059669')
           .fontSize(12)
           .font('Helvetica-Bold')
           .text(`Correct answer: ${processText(correctAnswer)}`, 90, currentY + 45, {
             width: doc.page.width - 150,
             height: 15,
             ellipsis: true
           });
      }

      currentY += 100;
    });

    // Footer
    doc.fillColor('#64748b')
       .fontSize(12)
       .font('Helvetica')
       .text(`📚 Generated for ${processText(testData.userName)} • ${date}`, 50, doc.page.height - 50, {
         align: 'center',
         width: doc.page.width - 100
       });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('PDF generation error:', error);
    res.status(500).json({ error: 'Failed to generate PDF' });
  }
});

// Legacy display page for backward compatibility (will show deprecation notice)
app.get('/display/:token', async (req, res) => {
  try {
    const { token } = req.params;
    const tokenData = qrTokens.get(token);

    if (!tokenData) {
      return res.status(404).send(`
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Invalid or Expired Link</title>
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                   background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                   margin: 0; padding: 20px; min-height: 100vh; display: flex; align-items: center; justify-content: center; }
            .container { background: white; padding: 40px; border-radius: 20px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); text-align: center; max-width: 400px; }
            h1 { color: #e53e3e; margin-bottom: 20px; }
            p { color: #666; line-height: 1.6; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🔒 Access Denied</h1>
            <p>This link is invalid or has expired. Please generate a new QR code.</p>
            <p><small>Note: QR codes now use permanent links that never expire.</small></p>
          </div>
        </body>
        </html>
      `);
    }

    const { item, bookTitle, chapterTitle } = tokenData;

    // Handle different item types
    if (item.type === 'link' && item.url) {
      // For links, redirect directly
      return res.redirect(item.url);
    }

    // For other types, show beautiful display page
    const displayPage = generateDisplayPage(item, bookTitle, chapterTitle);
    res.send(displayPage);

  } catch (error) {
    res.status(500).send('Internal Server Error');
  }
});

// Generate beautiful display page
function generateDisplayPage(item, bookTitle, chapterTitle) {
  const getItemIcon = (type) => {
    const icons = {
      question: '❓',
      test: '📝',
      text: '📄',
      image: '🖼️',
      link: '🔗',
      map: '🗺️',
      diagram: '📊',
      'timed-question': '⏰'
    };
    return icons[type] || '📋';
  };

  const renderItemContent = (item) => {
    switch (item.type) {
      case 'question':
        return `
          <div class="question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            ${item.options ? `
              <div class="options-container" id="optionsContainer">
                ${item.options.map((option, index) => `
                  <div class="option" data-index="${index}" onclick="selectOption(${index}, ${item.correctAnswer})">
                    <span class="option-letter">${String.fromCharCode(65 + index)}</span>
                    <span class="option-text">${option.text}</span>
                  </div>
                `).join('')}
              </div>
              <div id="result" class="result-container" style="display: none;">
                <div id="resultMessage" class="result-message"></div>
                <button onclick="resetQuestion()" class="reset-button">Try Again</button>
              </div>
            ` : ''}
          </div>
        `;
      case 'test':
        return `
          <div class="test-container">
            <div class="test-header">
              <h2 class="test-title">📝 ${item.title}</h2>
              <div class="test-progress">
                <span id="currentQuestion">1</span> of <span id="totalQuestions">${item.testQuestions?.length || 0}</span>
              </div>
            </div>

            <div class="test-content">
              ${item.testQuestions ? item.testQuestions.map((question, qIndex) => `
                <div class="test-question ${qIndex === 0 ? 'active' : 'hidden'}" data-question="${qIndex}">
                  <h3 class="question-text">${question.question}</h3>
                  <div class="test-options">
                    ${question.options.map((option, oIndex) => `
                      <div class="test-option" data-question="${qIndex}" data-option="${oIndex}" onclick="selectTestOption(${qIndex}, ${oIndex}, ${question.correctAnswer})">
                        <span class="option-letter">${String.fromCharCode(65 + oIndex)}</span>
                        <span class="option-text">${option.text}</span>
                      </div>
                    `).join('')}
                  </div>
                </div>
              `).join('') : '<p>No questions available</p>'}
            </div>

            <div class="test-navigation">
              <button id="prevBtn" onclick="previousQuestion()" disabled>Previous</button>
              <button id="nextBtn" onclick="nextQuestion()">Next</button>
              <button id="finishBtn" onclick="finishTest()" style="display: none;">Finish Test</button>
            </div>

            <div id="testResults" class="test-results" style="display: none;">
              <div class="results-header">
                <h3>Test Complete!</h3>
                <div class="score-circle">
                  <div class="score-text">
                    <span id="scoreNumber">0</span>/<span id="totalScore">${item.testQuestions?.length || 0}</span>
                  </div>
                </div>
              </div>

              <div class="results-list">
                <h4>Question Review:</h4>
                <div id="questionReview"></div>
              </div>

              <div class="results-actions">
                <button onclick="retakeTest()" class="retry-button">Retake Test</button>
                <button onclick="promptForNameAndDownload()" class="download-button">Download Results</button>
              </div>
            </div>
          </div>
        `;
      case 'text':
        return `
          <div class="text-container">
            <div class="text-content">${item.content || 'No content available'}</div>
          </div>
        `;
      case 'image':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🖼️</span>
              <p>Image: ${item.url || 'No image URL provided'}</p>
            </div>
          </div>
        `;
      case 'map':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">🗺️</span>
              <p>Map: ${item.title}</p>
              <small>Map image would be displayed here</small>
            </div>
          </div>
        `;
      case 'diagram':
        return `
          <div class="image-container">
            <div class="image-placeholder">
              <span class="image-icon">📊</span>
              <p>Diagram: ${item.title}</p>
              <small>Diagram image would be displayed here</small>
            </div>
          </div>
        `;
      case 'timed-question':
        return `
          <div class="timed-question-container">
            <h2 class="question-title">${item.question || item.title}</h2>
            <div class="timer-container">
              <div class="timer-display" id="timerDisplay">
                <span class="timer-icon">⏰</span>
                <span class="timer-text" id="timerText">${item.revealTimeSeconds || 30}</span>
                <span class="timer-label">seconds</span>
              </div>
            </div>
            <div class="answer-container" id="answerContainer" style="display: none;">
              <h3 class="answer-title">Answer:</h3>
              <div class="answer-content">${item.timedAnswer || 'No answer provided'}</div>
            </div>
            <button id="startTimerBtn" class="start-timer-button" onclick="startTimer()">Start Timer</button>
            <button id="resetTimerBtn" class="reset-timer-button" onclick="resetTimer()" style="display: none;">Reset</button>
          </div>
        `;
      default:
        return `<div class="default-content">Content not available</div>`;
    }
  };

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${item.title} - ${bookTitle}</title>
      <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          padding: 20px;
          line-height: 1.6;
        }

        .container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 20px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          overflow: hidden;
          animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
          from { opacity: 0; transform: translateY(30px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .header {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          text-align: center;
        }

        .item-icon {
          font-size: 3rem;
          margin-bottom: 15px;
          display: block;
        }

        .item-title {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 10px;
        }

        .breadcrumb {
          opacity: 0.9;
          font-size: 0.9rem;
        }

        .content {
          padding: 40px;
        }

        .question-container {
          text-align: center;
        }

        .question-title {
          font-size: 1.5rem;
          color: #1a202c;
          margin-bottom: 30px;
          font-weight: 600;
        }

        .options-container {
          display: grid;
          gap: 15px;
          max-width: 600px;
          margin: 0 auto;
        }

        .option {
          display: flex;
          align-items: center;
          padding: 20px;
          background: #f7fafc;
          border-radius: 12px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .option.correct {
          background: #f0fff4;
          border-color: #68d391;
          box-shadow: 0 4px 12px rgba(104, 211, 145, 0.2);
        }

        .option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
          box-shadow: 0 4px 12px rgba(252, 129, 129, 0.2);
        }

        .option.disabled {
          cursor: not-allowed;
          opacity: 0.7;
        }

        .option-letter {
          background: #4f46e5;
          color: white;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          margin-right: 15px;
          flex-shrink: 0;
        }

        .option.correct .option-letter {
          background: #38a169;
        }

        .option.incorrect .option-letter {
          background: #e53e3e;
        }

        .option-text {
          flex: 1;
          font-size: 1.1rem;
        }

        .correct-indicator {
          color: #38a169;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .incorrect-indicator {
          color: #e53e3e;
          font-size: 1.5rem;
          font-weight: bold;
        }

        .result-container {
          margin-top: 30px;
          text-align: center;
          padding: 20px;
          border-radius: 12px;
          animation: fadeIn 0.5s ease-out;
        }

        .result-message {
          font-size: 1.2rem;
          font-weight: 600;
          margin-bottom: 20px;
        }

        .result-message.correct {
          color: #38a169;
        }

        .result-message.incorrect {
          color: #e53e3e;
        }

        .reset-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .reset-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .text-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .text-content {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #2d3748;
          background: #f7fafc;
          padding: 30px;
          border-radius: 12px;
          border-left: 4px solid #4f46e5;
        }

        .image-container {
          text-align: center;
        }

        .image-placeholder {
          background: #f7fafc;
          border: 2px dashed #cbd5e0;
          border-radius: 12px;
          padding: 60px 30px;
          color: #718096;
        }

        .image-icon {
          font-size: 4rem;
          display: block;
          margin-bottom: 20px;
        }

        .timed-question-container {
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
        }

        .timer-container {
          margin: 30px 0;
        }

        .timer-display {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          color: white;
          padding: 30px;
          border-radius: 20px;
          display: inline-flex;
          align-items: center;
          gap: 15px;
          font-size: 2rem;
          font-weight: bold;
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }

        .timer-icon {
          font-size: 2.5rem;
        }

        .timer-text {
          font-size: 3rem;
          min-width: 80px;
        }

        .timer-label {
          font-size: 1.2rem;
          opacity: 0.9;
        }

        .answer-container {
          background: #f0fff4;
          border: 2px solid #68d391;
          border-radius: 12px;
          padding: 30px;
          margin: 30px 0;
          animation: slideDown 0.5s ease-out;
        }

        .answer-title {
          color: #38a169;
          font-size: 1.5rem;
          margin-bottom: 15px;
          font-weight: 600;
        }

        .answer-content {
          font-size: 1.2rem;
          line-height: 1.6;
          color: #2d3748;
          background: white;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #38a169;
        }

        @keyframes slideDown {
          from { opacity: 0; transform: translateY(-20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .start-timer-button, .reset-timer-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 15px 30px;
          border-radius: 10px;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          margin: 10px;
        }

        .start-timer-button:hover, .reset-timer-button:hover {
          background: #4338ca;
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .reset-timer-button {
          background: #6b7280;
        }

        .reset-timer-button:hover {
          background: #4b5563;
        }

        .footer {
          background: #f7fafc;
          padding: 20px 40px;
          text-align: center;
          color: #718096;
          font-size: 0.9rem;
          border-top: 1px solid #e2e8f0;
        }

        /* Test Styles */
        .test-container {
          max-width: 700px;
          margin: 0 auto;
        }

        .test-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 30px;
          padding-bottom: 20px;
          border-bottom: 2px solid #e2e8f0;
        }

        .test-title {
          font-size: 1.8rem;
          color: #1a202c;
          font-weight: 600;
        }

        .test-progress {
          background: #4f46e5;
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-weight: 600;
        }

        .test-content {
          margin-bottom: 30px;
        }

        .test-question {
          animation: fadeIn 0.3s ease-out;
        }

        .test-question.hidden {
          display: none;
        }

        .question-text {
          font-size: 1.4rem;
          color: #2d3748;
          margin-bottom: 25px;
          font-weight: 600;
          line-height: 1.5;
        }

        .test-options {
          display: grid;
          gap: 12px;
        }

        .test-option {
          display: flex;
          align-items: center;
          padding: 16px;
          background: #f7fafc;
          border-radius: 10px;
          border: 2px solid #e2e8f0;
          transition: all 0.3s ease;
          cursor: pointer;
        }

        .test-option:hover {
          background: #edf2f7;
          border-color: #cbd5e0;
          transform: translateY(-1px);
        }

        .test-option.selected {
          background: #ebf8ff;
          border-color: #4299e1;
        }

        .test-option.correct {
          background: #f0fff4;
          border-color: #68d391;
        }

        .test-option.incorrect {
          background: #fed7d7;
          border-color: #fc8181;
        }

        .test-navigation {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 30px;
        }

        .test-navigation button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .test-navigation button:hover:not(:disabled) {
          background: #4338ca;
          transform: translateY(-2px);
        }

        .test-navigation button:disabled {
          background: #9ca3af;
          cursor: not-allowed;
          transform: none;
        }

        .test-results {
          text-align: center;
          animation: fadeIn 0.5s ease-out;
        }

        .results-header {
          margin-bottom: 30px;
        }

        .results-header h3 {
          font-size: 2rem;
          color: #1a202c;
          margin-bottom: 20px;
        }

        .score-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: conic-gradient(#4f46e5 0deg, #4f46e5 var(--score-angle, 0deg), #e2e8f0 var(--score-angle, 0deg));
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 20px;
          position: relative;
        }

        .score-circle::before {
          content: '';
          width: 80px;
          height: 80px;
          background: white;
          border-radius: 50%;
          position: absolute;
        }

        .score-text {
          position: relative;
          z-index: 1;
          font-size: 1.5rem;
          font-weight: bold;
          color: #1a202c;
        }

        .results-list {
          text-align: left;
          margin-bottom: 30px;
        }

        .results-list h4 {
          font-size: 1.3rem;
          margin-bottom: 15px;
          color: #2d3748;
        }

        .question-result {
          display: flex;
          align-items: center;
          padding: 12px;
          margin-bottom: 8px;
          border-radius: 8px;
          background: #f7fafc;
        }

        .question-result.correct {
          background: #f0fff4;
          color: #38a169;
        }

        .question-result.incorrect {
          background: #fed7d7;
          color: #e53e3e;
        }

        .results-actions {
          display: flex;
          gap: 15px;
          justify-content: center;
        }

        .retry-button, .download-button {
          background: #4f46e5;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .download-button {
          background: #059669;
        }

        .retry-button:hover {
          background: #4338ca;
        }

        .download-button:hover {
          background: #047857;
        }

        @media (max-width: 768px) {
          body { padding: 10px; }
          .container { border-radius: 15px; }
          .header { padding: 20px; }
          .item-title { font-size: 1.5rem; }
          .content { padding: 20px; }
          .question-title { font-size: 1.3rem; }
          .option { padding: 15px; }
          .option-text { font-size: 1rem; }
          .text-content { font-size: 1.1rem; padding: 20px; }
          .test-header { flex-direction: column; gap: 15px; text-align: center; }
          .test-title { font-size: 1.5rem; }
          .question-text { font-size: 1.2rem; }
          .results-actions { flex-direction: column; }
        }
      </style>
      <script>
        function selectOption(selectedIndex, correctAnswer) {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');
          const resultMessage = document.getElementById('resultMessage');

          // Disable all options
          options.forEach(option => {
            option.classList.add('disabled');
            option.onclick = null;
          });

          // Mark selected option
          options[selectedIndex].classList.add('selected');

          // Show correct/incorrect styling
          if (selectedIndex === correctAnswer) {
            options[selectedIndex].classList.add('correct');
            options[selectedIndex].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '🎉 Correct! Well done!';
            resultMessage.className = 'result-message correct';
          } else {
            options[selectedIndex].classList.add('incorrect');
            options[selectedIndex].innerHTML += '<span class="incorrect-indicator">✗</span>';
            options[correctAnswer].classList.add('correct');
            options[correctAnswer].innerHTML += '<span class="correct-indicator">✓</span>';
            resultMessage.textContent = '❌ Incorrect. The correct answer is highlighted.';
            resultMessage.className = 'result-message incorrect';
          }

          // Show result
          resultContainer.style.display = 'block';
        }

        function resetQuestion() {
          const options = document.querySelectorAll('.option');
          const resultContainer = document.getElementById('result');

          // Reset all options
          options.forEach((option, index) => {
            option.className = 'option';
            option.onclick = () => selectOption(index, ${item.correctAnswer || 0});
            // Remove indicators
            const indicators = option.querySelectorAll('.correct-indicator, .incorrect-indicator');
            indicators.forEach(indicator => indicator.remove());
          });

          // Hide result
          resultContainer.style.display = 'none';
        }

        // Timed question functionality
        let timerInterval;
        let timeRemaining = ${item.revealTimeSeconds || 30};

        function startTimer() {
          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          startBtn.style.display = 'none';
          resetBtn.style.display = 'inline-block';

          timerInterval = setInterval(() => {
            timeRemaining--;
            timerText.textContent = timeRemaining;

            if (timeRemaining <= 0) {
              clearInterval(timerInterval);
              answerContainer.style.display = 'block';
              timerText.textContent = '0';
              resetBtn.textContent = 'Try Again';
            }
          }, 1000);
        }

        function resetTimer() {
          clearInterval(timerInterval);
          timeRemaining = ${item.revealTimeSeconds || 30};

          const startBtn = document.getElementById('startTimerBtn');
          const resetBtn = document.getElementById('resetTimerBtn');
          const timerText = document.getElementById('timerText');
          const answerContainer = document.getElementById('answerContainer');

          timerText.textContent = timeRemaining;
          answerContainer.style.display = 'none';
          startBtn.style.display = 'inline-block';
          resetBtn.style.display = 'none';
          resetBtn.textContent = 'Reset';
        }

        // Test functionality
        let currentQuestionIndex = 0;
        let testAnswers = [];
        let testQuestions = ${JSON.stringify(item.testQuestions || [])};

        function selectTestOption(questionIndex, optionIndex, correctAnswer) {
          const questionDiv = document.querySelector(\`[data-question="\${questionIndex}"]\`);
          const options = questionDiv.querySelectorAll('.test-option');

          // Clear previous selections for this question
          options.forEach(opt => {
            opt.classList.remove('selected');
          });

          // Mark selected option
          options[optionIndex].classList.add('selected');

          // Store answer
          testAnswers[questionIndex] = optionIndex;

          // Update navigation buttons
          updateNavigationButtons();
        }

        function nextQuestion() {
          if (currentQuestionIndex < testQuestions.length - 1) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show next question
            currentQuestionIndex++;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function previousQuestion() {
          if (currentQuestionIndex > 0) {
            // Hide current question
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.add('hidden');

            // Show previous question
            currentQuestionIndex--;
            document.querySelector(\`[data-question="\${currentQuestionIndex}"]\`).classList.remove('hidden');

            // Update progress
            document.getElementById('currentQuestion').textContent = currentQuestionIndex + 1;

            updateNavigationButtons();
          }
        }

        function updateNavigationButtons() {
          const prevBtn = document.getElementById('prevBtn');
          const nextBtn = document.getElementById('nextBtn');
          const finishBtn = document.getElementById('finishBtn');

          // Update previous button
          prevBtn.disabled = currentQuestionIndex === 0;

          // Update next/finish buttons
          if (currentQuestionIndex === testQuestions.length - 1) {
            nextBtn.style.display = 'none';
            finishBtn.style.display = 'inline-block';
          } else {
            nextBtn.style.display = 'inline-block';
            finishBtn.style.display = 'none';
          }
        }

        function finishTest() {
          // Calculate score
          let correctCount = 0;
          testQuestions.forEach((question, index) => {
            if (testAnswers[index] === question.correctAnswer) {
              correctCount++;
            }
          });

          // Hide test content
          document.querySelector('.test-content').style.display = 'none';
          document.querySelector('.test-navigation').style.display = 'none';

          // Show results
          const resultsDiv = document.getElementById('testResults');
          resultsDiv.style.display = 'block';

          // Update score display
          document.getElementById('scoreNumber').textContent = correctCount;
          document.getElementById('totalScore').textContent = testQuestions.length;

          // Update score circle
          const scorePercentage = (correctCount / testQuestions.length) * 100;
          const scoreAngle = (scorePercentage / 100) * 360;
          document.querySelector('.score-circle').style.setProperty('--score-angle', \`\${scoreAngle}deg\`);

          // Generate question review
          const reviewDiv = document.getElementById('questionReview');
          reviewDiv.innerHTML = testQuestions.map((question, index) => {
            const isCorrect = testAnswers[index] === question.correctAnswer;
            const userAnswer = testAnswers[index] !== undefined ? question.options[testAnswers[index]]?.text : 'Not answered';
            const correctAnswer = question.options[question.correctAnswer]?.text;

            return \`
              <div class="question-result \${isCorrect ? 'correct' : 'incorrect'}">
                <span style="margin-right: 10px;">\${isCorrect ? '✓' : '✗'}</span>
                <div>
                  <strong>Q\${index + 1}:</strong> \${question.question}<br>
                  <small>Your answer: \${userAnswer}</small>
                  \${!isCorrect ? \`<br><small>Correct answer: \${correctAnswer}</small>\` : ''}
                </div>
              </div>
            \`;
          }).join('');
        }

        function retakeTest() {
          // Reset test state
          currentQuestionIndex = 0;
          testAnswers = [];

          // Hide results
          document.getElementById('testResults').style.display = 'none';

          // Show test content
          document.querySelector('.test-content').style.display = 'block';
          document.querySelector('.test-navigation').style.display = 'flex';

          // Reset all questions
          document.querySelectorAll('.test-question').forEach((q, index) => {
            q.classList.toggle('hidden', index !== 0);
            q.querySelectorAll('.test-option').forEach(opt => {
              opt.classList.remove('selected');
            });
          });

          // Reset progress
          document.getElementById('currentQuestion').textContent = '1';

          // Reset navigation
          updateNavigationButtons();
        }

        function promptForNameAndDownload() {
          const userName = prompt('Please enter your name for the test results:');
          if (userName && userName.trim() !== '') {
            downloadResults(userName.trim());
          }
        }

        function downloadResults(userName = 'Student') {
          // Get score values from DOM elements
          const scoreElement = document.getElementById('scoreNumber');
          const totalElement = document.getElementById('totalScore');
          const currentScore = scoreElement ? parseInt(scoreElement.textContent) : 0;
          const currentTotal = totalElement ? parseInt(totalElement.textContent) : 0;

          // Generate PDF on server and download
          const testData = {
            userName: userName,
            testTitle: '${item.title}',
            bookTitle: '${bookTitle}',
            chapterTitle: '${chapterTitle}',
            score: currentScore,
            total: currentTotal,
            questions: testQuestions,
            userAnswers: testAnswers,
            date: new Date().toISOString()
          };

          // Create form and submit to server for PDF generation
          const form = document.createElement('form');
          form.method = 'POST';
          form.action = '/api/generate-test-pdf';
          form.style.display = 'none';

          const input = document.createElement('input');
          input.type = 'hidden';
          input.name = 'testData';
          input.value = JSON.stringify(testData);

          form.appendChild(input);
          document.body.appendChild(form);
          form.submit();
          document.body.removeChild(form);
        }

      </script>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <span class="item-icon">${getItemIcon(item.type)}</span>
          <h1 class="item-title">${item.title}</h1>
          <div class="breadcrumb">${bookTitle} → ${chapterTitle}</div>
        </div>

        <div class="content">
          ${renderItemContent(item)}
        </div>

        <div class="footer">
          <p>📚 Accessed via QR Code • ${new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

console.log('Starting server...');

const server = app.listen(PORT, '0.0.0.0', () => {
  const networkIP = getNetworkIP();
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`🌐 Network access: http://${networkIP}:${PORT}`);
  console.log(`📚 API available at: http://${networkIP}:${PORT}/api/books`);
  console.log(`📱 QR codes will use: http://${networkIP}:${PORT}`);
});

// Keep the server alive with a simple interval
setInterval(() => {
  // This keeps the event loop active
}, 30000);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});
