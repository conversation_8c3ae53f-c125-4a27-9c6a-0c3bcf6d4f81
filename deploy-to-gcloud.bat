@echo off
REM Google Cloud deployment script for textbook-platform

REM Configuration
set PROJECT_ID=textbook-platform
set SERVICE_NAME=textbook-platform
set REGION=us-central1

echo 🚀 Deploying textbook-platform to Google Cloud Run...

REM Check if gcloud is installed
where gcloud >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Google Cloud CLI is not installed. Please install it first:
    echo    https://cloud.google.com/sdk/docs/install
    exit /b 1
)

REM Set the project
echo 📋 Setting project to %PROJECT_ID%...
gcloud config set project %PROJECT_ID%

REM Enable required APIs
echo 🔧 Enabling required APIs...
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable firestore.googleapis.com

REM Build and deploy
echo 🏗️  Building and deploying to Cloud Run...
gcloud run deploy %SERVICE_NAME% ^
    --source . ^
    --platform managed ^
    --region %REGION% ^
    --allow-unauthenticated ^
    --port 3001 ^
    --memory 1Gi ^
    --cpu 1 ^
    --max-instances 10 ^
    --set-env-vars NODE_ENV=production

echo ✅ Deployment complete!
echo 🌐 Your app should be available at:
gcloud run services describe %SERVICE_NAME% --region %REGION% --format "value(status.url)"
